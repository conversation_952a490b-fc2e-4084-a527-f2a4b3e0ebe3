<view class="container">
  <!-- 订单状态切换标签 -->
  <view class="order-tabs">
    <view wx:for="{{orderTabs}}" wx:key="status" class="tab-item {{currentTab === item.status ? 'active' : ''}}" bindtap="switchTab" data-status="{{item.status}}">
      {{item.name}}
    </view>
  </view>
  <custom-modal
    show="{{showModal}}"
    title="{{modalTitle}}"
    content="{{modalContent}}"
    buttons="{{modalButtons}}"
    links="{{modalLinks}}"
    bind:confirm="onModalConfirm"
    bind:cancel="handleModalCancel"
    bind:modalConfirm="handleModalConfirm"
    bind:modalCancel="handleModalCancel"
  ></custom-modal>
  <!-- 订单列表 -->
  <scroll-view scroll-y class="order-list" bindscrolltolower="loadMoreOrders">
    <block wx:if="{{orderList.length > 0}}">
      <view wx:for="{{orderList}}" wx:key="id" class="order-item">
        <view class="order-header">
          <text class="order-number">订单号: {{item.sn}}</text>
          <text class="order-status">{{item.status}}</text>
        </view>

        <view class="order-content" data-orderid="{{item.id}}" data-orderdetail="{{item}}" bind:tap="viewOrderDetail">
          <image class="product-image" src="{{item.orderDetails[0].service.logo}}"></image>
          <view class="product-info">
            <view class="flex align-center justify-between">
              <text class="product-name">{{item.orderDetails[0].service.serviceName}}</text>
              <text class="product-price">¥ <text class="paid-money">{{item.totalFee}}</text></text>
            </view>
            <view class="flex align-center justify-between">
              <text wx:if="{{!!item.orderDetails[0].additionalServices.length}}" class="product-service">增项服务：<text wx:for="{{item.orderDetails[0].additionalServices}}" wx:for-item="val" wx:key="val.id">{{val.name}};</text></text>
              <text wx:else class="product-service">增项服务：无</text>
              <!-- <text class="product-quantity">x 1</text> -->
            </view>
          </view>
        </view>

        <view class="flex align-center justify-between magin-bottom">
          <text>期待上门时间：{{item.serviceTimeText || '待预约'}}</text>
          <!-- <text>实付款: ¥ <text class="paid-money">{{item.totalPrice}}</text></text> -->
        </view>
        <view class="flex align-center justify-between magin-bottom">
          <text>创建时间：{{item.createdAt || ''}}</text>
          <!-- <text>实付款: ¥ <text class="paid-money">{{item.totalPrice}}</text></text> -->
        </view>

        <view class="flex align-center justify-between">
          <view class="more-btn" bindtap="toggleOrderActions" data-order-id="{{item.id}}">更多</view>
          <view class='order-actions'>
            <!-- 根据订单状态显示不同的操作按钮 -->
            <!-- <view class="action-btn" bindtap="reschedule" data-order-id="{{item.orderId}}">
              更改时间
            </view> -->
            <!-- <view class="action-btn" bindtap="confirmReceipt" data-order-id="{{item.orderId}}">
              增加服务
            </view> -->
            <block wx:if="{{item.status === '待付款'}}">
              <view class="action-btn" bindtap="cancelOrder" data-order-id="{{item.id}}">
                取消订单
              </view>
              <view class="action-btn" bindtap="payOrder" data-sn="{{item.sn}}">
                去付款
              </view>
            </block>

            <block wx:if="{{item.status === '待接单'}}">
              <view class="action-btn" bindtap="refund" data-sn="{{item.sn}}">
                退款
              </view>
            </block>

            <block wx:if="{{item.status === '待服务' || item.status === '已出发'}}">
              <view class="action-btn" bindtap="refundRequest" data-sn="{{item.sn}}">
                申请退款
              </view>
            </block>

            <block wx:if="{{item.status === 'paid'}}">
              <view class="action-btn blue-btn" bindtap="confirmReceipt" data-order-id="{{item.id}}">
                催接单
              </view>
            </block>

            <block wx:if="{{item.status === '已完成'}}">
              <view wx:if="{{!item.hasReview}}" class="action-btn blue-btn" bindtap="reviewOrder" data-order-id="{{item.id}}">
                去评价
              </view>
              <view wx:if="{{item.hasReview}}" class="action-btn gray-btn" bindtap="viewReview" data-order-id="{{item.id}}">
                查看评价
              </view>
            </block>

            <block wx:if="{{item.status === '已评价'}}">
              <view class="action-btn gray-btn" bindtap="viewReview" data-order-id="{{item.id}}">
                查看评价
              </view>
            </block>
          </view>
        </view>

        <!-- 更多操作弹窗 -->
        <view wx:if="{{item.showMoreActions}}" class="more-actions-dropdown">
          <view class="dropdown-item" bindtap="viewOrderDetail" data-order-id="{{item.id}}">
            更改服务地址
          </view>
          <view class="dropdown-item" bindtap="toggleOrderActions" data-order-id="{{item.id}}">
            更换服务人员
          </view>
          <view class="dropdown-item" bindtap="cancelOrder" data-order-id="{{item.id}}">
            取消订单
          </view>
        </view>
      </view>
    </block>

    <view wx:else class="empty-list">
      <image src="//xian7.zos.ctyun.cn/pet/static/noreder.png" class="empty-image"></image>
      <text class="empty-text">暂无订单</text>
    </view>
  </scroll-view>
</view>